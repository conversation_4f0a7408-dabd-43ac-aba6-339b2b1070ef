<?php

namespace App\Http\Controllers\Frontend;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use App\Models\Partners as PartnerModel; 
use App\Models\TagToken;
use App\Models\GiftValues;
use App\Services\Tillo;
use App\Models\PartnerTags;
use App\Models\UserVouchers;
use DB;
use Auth;
class PartnerFront extends BaseController
{
    // this handles the view screen of where to spend page
    public function whereToSpend()
    {   
        $user_data = Auth::user();
        if(isset($user_data))
        {   
            $udata = UserVouchers::select('voucher_code')->where('user_id',$user_data->id)->get()->toArray();
            $vouchers = array_column($udata,'voucher_code');

            $partner_data  = PartnerModel::with('category.categoryImages')
                            ->select('partners.*', 'pt.partner_id', 'tt.tag_id', 'tt.tokens')
                            ->leftJoin('partner_tags as pt', 'pt.partner_id', '=', 'partners.id')
                            ->join('tag_tokens as tt', 'tt.tag_id', '=', 'pt.tag_id')
                            ->where('partners.status', PartnerModel::ACTIVE)
                            ->whereIn('tt.tokens', $vouchers)
                            ->orderBy('partners.partner_category_id')
                            ->groupBy('partners.id')
                            ->get();
            if($partner_data->count()==0)
            {
                 $partner_data = PartnerModel::with('category.categoryImages')->where('partners.status',PartnerModel::ACTIVE)->where('partners.show_default', PartnerModel::SHOWDEFAULT)->orderBy('partners.partner_category_id')->get();
            }                 
            
        }else{
            $partner_data = PartnerModel::with('category.categoryImages')->where('partners.status',PartnerModel::ACTIVE)->where('partners.show_default', PartnerModel::SHOWDEFAULT)->orderBy('partners.partner_category_id')->get();
        }
        $partner_array = array();
        foreach($partner_data as $partner)
        {
            $partner_array[$partner->partner_category_id]['category_name'] =$partner->category->category_name;
            $partner_array[$partner->partner_category_id]['category_description'] =$partner->category->category_description;
            $partner_array[$partner->partner_category_id]['images'] = $partner->category->categoryImages;
            $partner_array[$partner->partner_category_id]['partners'][] = $partner;
        }
     
        return view('/frontend/pages/where_to_spend', [
                'partner_listing' => $partner_array,
                'page_no'=>3
          ]);
    }
    // this handles the partner detail page screen
    public function partnerDetail($slug='',Tillo $tillo)
    {
        $partner_data = PartnerModel::where('slug',$slug)->where('partners.status',PartnerModel::ACTIVE)->first();
        if(!$partner_data){
            return redirect('/404');
        }
        $user_data = Auth::user();
        if(isset($user_data))
        {   
            $tagIDS = $this->getTagsDataByUserId($user_data->id);

            $p_data = PartnerTags::select('partner_id')->whereIn('tag_id',$tagIDS)->groupBy('partner_id')->get()->toArray();
            $pIds = array_column($p_data,'partner_id');
            if(count($p_data)==0)
            {
                $partner_id = $partner_data->id;
                $partner_data = PartnerModel::where('slug',$slug)->where('partners.status',PartnerModel::ACTIVE)->where('partners.show_default', PartnerModel::SHOWDEFAULT)->where('id',$partner_id)->first();
                if(empty($partner_data))
                {
                     return redirect('/where-to-spend');
                }  

            }else if (!in_array($partner_data->id,  $pIds )) {
                return redirect('/where-to-spend');
            }
        }else {
            $partner_data = PartnerModel::where('slug',$slug)->where('partners.status',PartnerModel::ACTIVE)->where('partners.show_default', PartnerModel::SHOWDEFAULT)->first();
        }
        if(!$partner_data){
            return redirect('/404');
        }
        $partner_code = $partner_data->partner_code;
        $partnerOpenAmountData = $partner_data->partnerOpenAmounts();
        $partnerOpenAmountData = empty($partnerOpenAmountData) ? [] : $partnerOpenAmountData->toArray();
        // gets all gift codes of partner
        $gift_values = GiftValues::where('status',GiftValues::ACTIVE)->where(DB::raw('date(expiry_date)'),'>',date('Y-m-d'))->where('partner_code',$partner_code)->groupBy('gift_value')->orderBy('gift_value','ASC')->get();
        $tillo_api_data = [];
        $partner_status = $partner_data->status;
        if($partner_status==2)
        {
         $gift_values = array();
        }
        if($partner_data->partner_type == 2)
        {
         $tillo_api_data = $tillo->getBrandDetails($slug);
        }
        
        return view('/frontend/pages/partner_detail', [
            'partner_data' => $partner_data,
            'partner_open_amounts' => $partnerOpenAmountData,
            'partner_gift'=>$gift_values,
            'meta_keywords'=>$partner_data->meta_keywords,
            'meta_description'=>$partner_data->meta_description,
            'tillo_api_data'=> $tillo_api_data
          ]);
       
    }
    public function getTagsDataByUserId($userID){
        $udata = UserVouchers::select('voucher_code')->where('user_id',$userID)->get()->toArray();
        $vouchers = array_column($udata,'voucher_code');
        $tagViaToken = TagToken::select('tag_id')->whereIn('tokens',$vouchers)->get()->toArray();
        $tagIDS = array_column($tagViaToken,'tag_id');

        return $tagIDS;

    }
}
